#!/usr/bin/env python3
"""
Script to validate that all content IDs are properly linked:
1. Check that theme _id exists and is traceable in sets
2. Check that set _id exists and is traceable in items
3. Check that set tasks array contains proper item _ids
4. Generate comprehensive stats and validation report
"""

import json
import os
from collections import defaultdict

def load_json_file(file_path):
    """Load and return JSON content from file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def validate_theme_folder(theme_folder):
    """Validate all files in a theme folder and return stats"""
    print(f"\n=== Validating {theme_folder} ===")
    
    stats = {
        'theme_name': '',
        'theme_id': '',
        'sets': [],
        'total_items': 0,
        'errors': [],
        'warnings': []
    }
    
    theme_path = os.path.join(theme_folder, 'theme.json')
    
    # Load theme
    theme = load_json_file(theme_path)
    if not theme:
        stats['errors'].append(f"Failed to load theme.json")
        return stats
    
    stats['theme_name'] = theme.get('name', 'Unknown')
    stats['theme_id'] = theme.get('_id', 'Missing')
    
    print(f"Theme: {stats['theme_name']} (ID: {stats['theme_id']})")
    
    # Find all set files
    set_files = [f for f in os.listdir(theme_folder) if f.startswith('set') and f.endswith('.json') and 'items' not in f]
    
    for set_file in sorted(set_files):
        set_path = os.path.join(theme_folder, set_file)
        items_file = set_file.replace('.json', '_items.json')
        items_path = os.path.join(theme_folder, items_file)
        
        # Load set
        content_set = load_json_file(set_path)
        if not content_set:
            stats['errors'].append(f"Failed to load {set_file}")
            continue
        
        set_stats = {
            'set_file': set_file,
            'set_id': content_set.get('_id', 'Missing'),
            'set_title': content_set.get('title', 'Unknown'),
            'theme_id_ref': content_set.get('theme_id', 'Missing'),
            'tasks': content_set.get('tasks', []),
            'total_items_declared': content_set.get('total_items', 0),
            'items': [],
            'items_count': 0
        }
        
        # Validate theme_id reference
        if set_stats['theme_id_ref'] != stats['theme_id']:
            stats['errors'].append(f"Set {set_file} theme_id mismatch: {set_stats['theme_id_ref']} != {stats['theme_id']}")
        
        print(f"  Set: {set_stats['set_title']} (ID: {set_stats['set_id']})")
        print(f"    Theme ID ref: {set_stats['theme_id_ref']}")
        print(f"    Tasks array: {len(set_stats['tasks'])} items")
        
        # Load items
        if os.path.exists(items_path):
            items = load_json_file(items_path)
            if items:
                set_stats['items_count'] = len(items)
                stats['total_items'] += len(items)
                
                for item in items:
                    item_stats = {
                        'item_id': item.get('_id', 'Missing'),
                        'task_set_id_ref': item.get('task_set_id', 'Missing'),
                        'title': item.get('title', 'Unknown'),
                        'type': item.get('type', 'Unknown')
                    }
                    
                    # Validate task_set_id reference
                    if item_stats['task_set_id_ref'] != set_stats['set_id']:
                        stats['errors'].append(f"Item {item_stats['item_id']} task_set_id mismatch: {item_stats['task_set_id_ref']} != {set_stats['set_id']}")
                    
                    set_stats['items'].append(item_stats)
                
                # Validate tasks array contains all item IDs
                item_ids = [item['item_id'] for item in set_stats['items']]
                tasks_ids = set_stats['tasks']
                
                if set(item_ids) != set(tasks_ids):
                    stats['errors'].append(f"Set {set_file} tasks array doesn't match item IDs")
                    print(f"    ERROR: Tasks array mismatch!")
                    print(f"      Item IDs: {item_ids}")
                    print(f"      Tasks array: {tasks_ids}")
                else:
                    print(f"    ✓ Tasks array matches item IDs")
                
                # Validate total_items count
                if set_stats['total_items_declared'] != set_stats['items_count']:
                    stats['warnings'].append(f"Set {set_file} total_items mismatch: declared {set_stats['total_items_declared']}, actual {set_stats['items_count']}")
                
                print(f"    Items: {set_stats['items_count']} loaded")
                for item in set_stats['items']:
                    print(f"      - {item['title']} ({item['type']}) ID: {item['item_id']}")
            else:
                stats['errors'].append(f"Failed to load {items_file}")
        else:
            stats['errors'].append(f"Items file {items_file} not found")
        
        stats['sets'].append(set_stats)
    
    return stats

def generate_summary_report(all_stats):
    """Generate comprehensive summary report"""
    print("\n" + "="*80)
    print("COMPREHENSIVE VALIDATION REPORT")
    print("="*80)
    
    total_themes = len(all_stats)
    total_sets = sum(len(theme['sets']) for theme in all_stats)
    total_items = sum(theme['total_items'] for theme in all_stats)
    total_errors = sum(len(theme['errors']) for theme in all_stats)
    total_warnings = sum(len(theme['warnings']) for theme in all_stats)
    
    print(f"\nOVERALL STATISTICS:")
    print(f"  Total Themes: {total_themes}")
    print(f"  Total Sets: {total_sets}")
    print(f"  Total Items: {total_items}")
    print(f"  Total Errors: {total_errors}")
    print(f"  Total Warnings: {total_warnings}")
    
    print(f"\nTHEME BREAKDOWN:")
    for theme_stats in all_stats:
        print(f"  {theme_stats['theme_name']} ({theme_stats['theme_id']}):")
        print(f"    Sets: {len(theme_stats['sets'])}")
        print(f"    Items: {theme_stats['total_items']}")
        print(f"    Errors: {len(theme_stats['errors'])}")
        print(f"    Warnings: {len(theme_stats['warnings'])}")
    
    if total_errors > 0:
        print(f"\nERRORS FOUND:")
        for theme_stats in all_stats:
            if theme_stats['errors']:
                print(f"  {theme_stats['theme_name']}:")
                for error in theme_stats['errors']:
                    print(f"    - {error}")
    
    if total_warnings > 0:
        print(f"\nWARNINGS FOUND:")
        for theme_stats in all_stats:
            if theme_stats['warnings']:
                print(f"  {theme_stats['theme_name']}:")
                for warning in theme_stats['warnings']:
                    print(f"    - {warning}")
    
    # ID Traceability Report
    print(f"\nID TRACEABILITY REPORT:")
    for theme_stats in all_stats:
        print(f"\n{theme_stats['theme_name']} (Theme ID: {theme_stats['theme_id']}):")
        for set_stats in theme_stats['sets']:
            print(f"  └─ {set_stats['set_title']} (Set ID: {set_stats['set_id']})")
            print(f"     Theme ID Reference: {set_stats['theme_id_ref']} {'✓' if set_stats['theme_id_ref'] == theme_stats['theme_id'] else '✗'}")
            print(f"     Tasks Array: {len(set_stats['tasks'])} items")
            for item in set_stats['items']:
                print(f"     └─ {item['title']} (Item ID: {item['item_id']})")
                print(f"        Task Set ID Reference: {item['task_set_id_ref']} {'✓' if item['task_set_id_ref'] == set_stats['set_id'] else '✗'}")
    
    if total_errors == 0 and total_warnings == 0:
        print(f"\n🎉 ALL VALIDATIONS PASSED! Content structure is properly linked.")
    else:
        print(f"\n⚠️  VALIDATION ISSUES FOUND. Please review and fix the errors above.")

def main():
    """Main validation function"""
    content_dir = 'content'
    
    if not os.path.exists(content_dir):
        print(f"Error: {content_dir} directory not found")
        return
    
    # Get all theme folders
    theme_folders = [d for d in os.listdir(content_dir) 
                    if os.path.isdir(os.path.join(content_dir, d))]
    
    print(f"Found {len(theme_folders)} theme folders: {theme_folders}")
    
    all_stats = []
    
    # Validate each theme folder
    for theme_folder in sorted(theme_folders):
        theme_path = os.path.join(content_dir, theme_folder)
        stats = validate_theme_folder(theme_path)
        all_stats.append(stats)
    
    # Generate comprehensive report
    generate_summary_report(all_stats)

if __name__ == "__main__":
    main()
