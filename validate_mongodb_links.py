#!/usr/bin/env python3
"""
Script to validate MongoDB content links using PyMongo queries:
1. Query themes collection and verify theme _ids
2. Query curated_content_set and verify theme_id references
3. Query curated_content_items and verify task_set_id references
4. Validate that set tasks arrays contain proper item _ids
5. Generate comprehensive stats using MongoDB aggregation
"""

from pymongo import MongoClient
from bson import ObjectId
from collections import defaultdict

# MongoDB connection
MONGO_URI = "mongodb://localhost:27017/"
DATABASE_NAME = "test_nepali_app"

def connect_to_mongodb():
    """Connect to MongoDB and return database"""
    try:
        client = MongoClient(MONGO_URI)
        db = client[DATABASE_NAME]
        # Test connection
        client.admin.command('ping')
        print(f"✓ Connected to MongoDB database: {DATABASE_NAME}")
        return db
    except Exception as e:
        print(f"✗ Error connecting to MongoDB: {e}")
        return None

def validate_theme_references(db):
    """Validate that all sets have valid theme_id references"""
    print("\n=== VALIDATING THEME REFERENCES ===")
    
    # Get all themes
    themes = list(db.themes.find({}, {'_id': 1, 'name': 1, 'name_en': 1}))
    theme_ids = {str(theme['_id']) for theme in themes}
    
    print(f"Found {len(themes)} themes:")
    for theme in themes:
        print(f"  - {theme['name']} ({theme['name_en']}) ID: {theme['_id']}")
    
    # Get all sets and check theme_id references
    sets = list(db.curated_content_set.find({}, {'_id': 1, 'title': 1, 'theme_id': 1}))
    
    print(f"\nValidating {len(sets)} content sets:")
    valid_refs = 0
    invalid_refs = []
    
    for content_set in sets:
        theme_id_ref = str(content_set['theme_id'])
        if theme_id_ref in theme_ids:
            print(f"  ✓ {content_set['title']} -> Theme ID: {theme_id_ref}")
            valid_refs += 1
        else:
            print(f"  ✗ {content_set['title']} -> INVALID Theme ID: {theme_id_ref}")
            invalid_refs.append({
                'set_id': content_set['_id'],
                'set_title': content_set['title'],
                'invalid_theme_id': theme_id_ref
            })
    
    print(f"\nTheme Reference Summary:")
    print(f"  Valid references: {valid_refs}")
    print(f"  Invalid references: {len(invalid_refs)}")
    
    return invalid_refs

def validate_set_references(db):
    """Validate that all items have valid task_set_id references"""
    print("\n=== VALIDATING SET REFERENCES ===")
    
    # Get all sets
    sets = list(db.curated_content_set.find({}, {'_id': 1, 'title': 1, 'tasks': 1}))
    set_ids = {str(content_set['_id']) for content_set in sets}
    
    print(f"Found {len(sets)} content sets:")
    for content_set in sets:
        tasks_count = len(content_set.get('tasks', []))
        print(f"  - {content_set['title']} ID: {content_set['_id']} ({tasks_count} tasks)")
    
    # Get all items and check task_set_id references
    items = list(db.curated_content_items.find({}, {'_id': 1, 'title': 1, 'task_set_id': 1}))
    
    print(f"\nValidating {len(items)} content items:")
    valid_refs = 0
    invalid_refs = []
    
    for item in items:
        task_set_id_ref = str(item['task_set_id'])
        if task_set_id_ref in set_ids:
            print(f"  ✓ {item['title']} -> Set ID: {task_set_id_ref}")
            valid_refs += 1
        else:
            print(f"  ✗ {item['title']} -> INVALID Set ID: {task_set_id_ref}")
            invalid_refs.append({
                'item_id': item['_id'],
                'item_title': item['title'],
                'invalid_set_id': task_set_id_ref
            })
    
    print(f"\nSet Reference Summary:")
    print(f"  Valid references: {valid_refs}")
    print(f"  Invalid references: {len(invalid_refs)}")
    
    return invalid_refs

def validate_tasks_arrays(db):
    """Validate that set tasks arrays contain proper item _ids"""
    print("\n=== VALIDATING TASKS ARRAYS ===")
    
    # Get all sets with their tasks arrays
    sets = list(db.curated_content_set.find({}, {'_id': 1, 'title': 1, 'tasks': 1}))
    
    validation_results = []
    
    for content_set in sets:
        set_id = content_set['_id']
        set_title = content_set['title']
        tasks_array = content_set.get('tasks', [])
        
        print(f"\nValidating set: {set_title} (ID: {set_id})")
        print(f"  Tasks array contains {len(tasks_array)} item IDs")
        
        # Get actual items for this set
        actual_items = list(db.curated_content_items.find(
            {'task_set_id': set_id}, 
            {'_id': 1, 'title': 1}
        ))
        
        actual_item_ids = {item['_id'] for item in actual_items}
        tasks_array_ids = {ObjectId(task_id) if isinstance(task_id, str) else task_id for task_id in tasks_array}
        
        print(f"  Actual items in database: {len(actual_items)}")
        
        # Check if tasks array matches actual items
        missing_in_tasks = actual_item_ids - tasks_array_ids
        extra_in_tasks = tasks_array_ids - actual_item_ids
        
        result = {
            'set_id': set_id,
            'set_title': set_title,
            'tasks_array_count': len(tasks_array),
            'actual_items_count': len(actual_items),
            'missing_in_tasks': list(missing_in_tasks),
            'extra_in_tasks': list(extra_in_tasks),
            'is_valid': len(missing_in_tasks) == 0 and len(extra_in_tasks) == 0
        }
        
        if result['is_valid']:
            print(f"  ✓ Tasks array perfectly matches actual items")
        else:
            print(f"  ✗ Tasks array mismatch:")
            if missing_in_tasks:
                print(f"    Missing in tasks array: {len(missing_in_tasks)} items")
                for item_id in missing_in_tasks:
                    item = next((i for i in actual_items if i['_id'] == item_id), None)
                    if item:
                        print(f"      - {item['title']} (ID: {item_id})")
            if extra_in_tasks:
                print(f"    Extra in tasks array: {len(extra_in_tasks)} items")
                for item_id in extra_in_tasks:
                    print(f"      - ID: {item_id}")
        
        validation_results.append(result)
    
    return validation_results

def generate_aggregation_stats(db):
    """Generate comprehensive stats using MongoDB aggregation"""
    print("\n=== AGGREGATION STATISTICS ===")
    
    # Theme stats with set and item counts
    theme_stats = list(db.themes.aggregate([
        {
            '$lookup': {
                'from': 'curated_content_set',
                'localField': '_id',
                'foreignField': 'theme_id',
                'as': 'sets'
            }
        },
        {
            '$addFields': {
                'sets_count': {'$size': '$sets'},
                'set_ids': '$sets._id'
            }
        },
        {
            '$lookup': {
                'from': 'curated_content_items',
                'localField': 'set_ids',
                'foreignField': 'task_set_id',
                'as': 'items'
            }
        },
        {
            '$addFields': {
                'items_count': {'$size': '$items'}
            }
        },
        {
            '$project': {
                'name': 1,
                'name_en': 1,
                'sets_count': 1,
                'items_count': 1
            }
        }
    ]))
    
    print("Theme Statistics (with aggregated counts):")
    total_themes = len(theme_stats)
    total_sets = sum(theme['sets_count'] for theme in theme_stats)
    total_items = sum(theme['items_count'] for theme in theme_stats)
    
    for theme in theme_stats:
        print(f"  {theme['name']} ({theme['name_en']}):")
        print(f"    ID: {theme['_id']}")
        print(f"    Sets: {theme['sets_count']}")
        print(f"    Items: {theme['items_count']}")
    
    print(f"\nOverall Totals:")
    print(f"  Themes: {total_themes}")
    print(f"  Sets: {total_sets}")
    print(f"  Items: {total_items}")
    
    return theme_stats

def generate_traceability_report(db):
    """Generate detailed traceability report"""
    print("\n=== DETAILED TRACEABILITY REPORT ===")
    
    # Get complete hierarchy
    themes = list(db.themes.find({}, {'_id': 1, 'name': 1, 'name_en': 1}).sort('name', 1))
    
    for theme in themes:
        print(f"\n📁 THEME: {theme['name']} ({theme['name_en']})")
        print(f"   ID: {theme['_id']}")
        
        # Get sets for this theme
        sets = list(db.curated_content_set.find(
            {'theme_id': theme['_id']}, 
            {'_id': 1, 'title': 1, 'tasks': 1}
        ).sort('title', 1))
        
        for content_set in sets:
            print(f"   └─ 📂 SET: {content_set['title']}")
            print(f"      ID: {content_set['_id']}")
            print(f"      Tasks Array: {len(content_set.get('tasks', []))} items")
            
            # Get items for this set
            items = list(db.curated_content_items.find(
                {'task_set_id': content_set['_id']}, 
                {'_id': 1, 'title': 1, 'type': 1}
            ).sort('title', 1))
            
            for item in items:
                in_tasks = item['_id'] in content_set.get('tasks', [])
                status = "✓" if in_tasks else "✗"
                print(f"      └─ 📄 ITEM: {item['title']} ({item['type']}) {status}")
                print(f"         ID: {item['_id']}")

def main():
    """Main validation function"""
    db = connect_to_mongodb()
    if not db:
        return
    
    # Check if collections exist and have data
    collections_info = {
        'themes': db.themes.count_documents({}),
        'curated_content_set': db.curated_content_set.count_documents({}),
        'curated_content_items': db.curated_content_items.count_documents({})
    }
    
    print(f"\nCollection Document Counts:")
    for collection, count in collections_info.items():
        print(f"  {collection}: {count} documents")
    
    if sum(collections_info.values()) == 0:
        print("\n⚠️  No data found in collections. Please run load_to_mongodb.py first.")
        return
    
    # Run all validations
    theme_ref_errors = validate_theme_references(db)
    set_ref_errors = validate_set_references(db)
    tasks_validation = validate_tasks_arrays(db)
    
    # Generate aggregation stats
    theme_stats = generate_aggregation_stats(db)
    
    # Generate traceability report
    generate_traceability_report(db)
    
    # Final summary
    print("\n" + "="*80)
    print("FINAL VALIDATION SUMMARY")
    print("="*80)
    
    total_errors = len(theme_ref_errors) + len(set_ref_errors)
    invalid_tasks = sum(1 for result in tasks_validation if not result['is_valid'])
    
    print(f"Theme Reference Errors: {len(theme_ref_errors)}")
    print(f"Set Reference Errors: {len(set_ref_errors)}")
    print(f"Invalid Tasks Arrays: {invalid_tasks}")
    print(f"Total Issues: {total_errors + invalid_tasks}")
    
    if total_errors == 0 and invalid_tasks == 0:
        print(f"\n🎉 ALL MONGODB VALIDATIONS PASSED!")
        print(f"   All IDs are properly linked and traceable.")
    else:
        print(f"\n⚠️  VALIDATION ISSUES FOUND!")
        print(f"   Please review and fix the issues above.")

if __name__ == "__main__":
    main()
