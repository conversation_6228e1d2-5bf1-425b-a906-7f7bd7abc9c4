#!/usr/bin/env python3
"""
Script to load JSON content files into MongoDB collections:
1. Load theme files and insert into 'themes' collection
2. Load set files and insert into 'curated_content_set' collection  
3. Load item files and insert into 'curated_content_items' collection
4. Convert all _id fields to proper ObjectId format
5. Link theme_id and task_set_id references properly
6. Add tasks array to sets with proper ObjectId references
"""

import json
import os
from pymongo import MongoClient
from bson import ObjectId
from datetime import datetime, timezone

# MongoDB connection
MONGO_URI = "mongodb://localhost:27017/"
DATABASE_NAME = "test_nepali_app"

def connect_to_mongodb():
    """Connect to MongoDB and return database"""
    client = MongoClient(MONGO_URI)
    db = client[DATABASE_NAME]
    return db

def clear_collections(db):
    """Clear existing collections"""
    collections = ['themes', 'curated_content_set', 'curated_content_items']
    for collection_name in collections:
        result = db[collection_name].delete_many({})
        print(f"Cleared {result.deleted_count} documents from {collection_name}")

def load_theme_to_mongodb(theme_path, db):
    """Load theme file and insert into themes collection"""
    with open(theme_path, 'r', encoding='utf-8') as f:
        theme = json.load(f)
    
    # Convert _id to ObjectId
    theme['_id'] = ObjectId()
    
    # Update timestamps
    theme['created_at'] = datetime.now(timezone.utc)
    theme['updated_at'] = datetime.now(timezone.utc)
    
    # Insert into themes collection
    result = db.themes.insert_one(theme)
    print(f"Inserted theme: {theme['name']} with ID: {result.inserted_id}")
    
    return result.inserted_id

def load_set_to_mongodb(set_path, theme_id, db):
    """Load set file and insert into curated_content_set collection"""
    with open(set_path, 'r', encoding='utf-8') as f:
        content_set = json.load(f)
    
    # Convert _id to ObjectId
    content_set['_id'] = ObjectId()
    
    # Set theme_id reference
    content_set['theme_id'] = theme_id
    
    # Update timestamps
    content_set['created_at'] = datetime.now(timezone.utc)
    content_set['updated_at'] = datetime.now(timezone.utc)
    
    # Initialize empty tasks array (will be populated later)
    content_set['tasks'] = []
    
    # Insert into curated_content_set collection
    result = db.curated_content_set.insert_one(content_set)
    print(f"Inserted set: {content_set['title']} with ID: {result.inserted_id}")
    
    return result.inserted_id

def load_items_to_mongodb(items_path, set_id, db):
    """Load items file and insert into curated_content_items collection"""
    with open(items_path, 'r', encoding='utf-8') as f:
        items = json.load(f)
    
    item_ids = []
    
    for item in items:
        # Convert _id to ObjectId
        item['_id'] = ObjectId()
        item_ids.append(item['_id'])
        
        # Set task_set_id reference
        item['task_set_id'] = set_id
        
        # Add timestamps if not present
        item['created_at'] = datetime.now(timezone.utc)
        item['updated_at'] = datetime.now(timezone.utc)
        
        # Ensure all required fields are present
        item.setdefault('user_answer', None)
        item.setdefault('result', None)
        item.setdefault('remark', None)
        item.setdefault('scored', 0)
        item.setdefault('submitted', False)
        item.setdefault('submitted_at', None)
        item.setdefault('attempts_count', 0)
        item.setdefault('metadata', {})
    
    # Insert all items into curated_content_items collection
    if items:
        result = db.curated_content_items.insert_many(items)
        print(f"Inserted {len(result.inserted_ids)} items for set {set_id}")
    
    return item_ids

def update_set_with_tasks(set_id, task_ids, db):
    """Update set with task IDs"""
    result = db.curated_content_set.update_one(
        {'_id': set_id},
        {
            '$set': {
                'tasks': task_ids,
                'total_items': len(task_ids),
                'updated_at': datetime.now(timezone.utc)
            }
        }
    )
    print(f"Updated set {set_id} with {len(task_ids)} tasks")

def process_theme_folder(theme_folder, db):
    """Process a complete theme folder"""
    print(f"\n=== Processing {theme_folder} ===")
    
    theme_path = os.path.join(theme_folder, 'theme.json')
    
    # Load theme
    theme_id = load_theme_to_mongodb(theme_path, db)
    
    # Process all sets in the folder
    set_files = [f for f in os.listdir(theme_folder) if f.startswith('set') and f.endswith('.json') and 'items' not in f]
    
    for set_file in sorted(set_files):
        set_path = os.path.join(theme_folder, set_file)
        items_file = set_file.replace('.json', '_items.json')
        items_path = os.path.join(theme_folder, items_file)
        
        if os.path.exists(items_path):
            # Load set
            set_id = load_set_to_mongodb(set_path, theme_id, db)
            
            # Load items
            item_ids = load_items_to_mongodb(items_path, set_id, db)
            
            # Update set with task IDs
            update_set_with_tasks(set_id, item_ids, db)
        else:
            print(f"Warning: Items file {items_path} not found for {set_file}")

def main():
    """Main function to process all theme folders and load to MongoDB"""
    content_dir = 'content'
    
    if not os.path.exists(content_dir):
        print(f"Error: {content_dir} directory not found")
        return
    
    # Connect to MongoDB
    try:
        db = connect_to_mongodb()
        print(f"Connected to MongoDB database: {DATABASE_NAME}")
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        return
    
    # Clear existing collections
    print("\n=== Clearing existing collections ===")
    clear_collections(db)
    
    # Get all theme folders
    theme_folders = [d for d in os.listdir(content_dir) 
                    if os.path.isdir(os.path.join(content_dir, d))]
    
    print(f"\nFound {len(theme_folders)} theme folders: {theme_folders}")
    
    # Process each theme folder
    for theme_folder in sorted(theme_folders):
        theme_path = os.path.join(content_dir, theme_folder)
        process_theme_folder(theme_path, db)
    
    print("\n=== All themes loaded to MongoDB successfully! ===")
    
    # Generate summary
    print("\n=== SUMMARY ===")
    themes_count = db.themes.count_documents({})
    sets_count = db.curated_content_set.count_documents({})
    items_count = db.curated_content_items.count_documents({})
    
    print(f"Total themes: {themes_count}")
    print(f"Total content sets: {sets_count}")
    print(f"Total content items: {items_count}")
    
    print("\n=== Themes in database ===")
    for theme in db.themes.find({}, {'name': 1, 'name_en': 1}):
        print(f"Theme: {theme['name']} ({theme['name_en']}) - ID: {theme['_id']}")

if __name__ == "__main__":
    main()
