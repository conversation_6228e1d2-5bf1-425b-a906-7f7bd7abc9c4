#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix ObjectId issues in content files:
1. Convert theme _id to ObjectId format
2. Convert set _id to ObjectId format  
3. Convert item _id to ObjectId format
4. Link theme_id in sets to proper ObjectId
5. Add tasks array to sets with proper ObjectId references to items
"""

import json
import os
from bson import ObjectId
from datetime import datetime, timezone

def generate_object_id():
    """Generate a new ObjectId string in proper MongoDB format"""
    return ObjectId()

def fix_theme_file(theme_path):
    """Fix theme file ObjectId"""
    with open(theme_path, 'r', encoding='utf-8') as f:
        theme = json.load(f)

    # Generate new ObjectId for theme
    old_id = theme['_id']
    new_id = str(ObjectId())  # Convert to string for JSON serialization
    theme['_id'] = new_id

    # Update timestamps
    theme['created_at'] = datetime.now(timezone.utc).isoformat()
    theme['updated_at'] = datetime.now(timezone.utc).isoformat()

    with open(theme_path, 'w', encoding='utf-8') as f:
        json.dump(theme, f, ensure_ascii=False, indent=2)

    print(f"Fixed theme: {old_id} -> {new_id}")
    return new_id

def fix_set_file(set_path, theme_id):
    """Fix set file ObjectId and theme_id reference"""
    with open(set_path, 'r', encoding='utf-8') as f:
        content_set = json.load(f)

    # Generate new ObjectId for set
    old_id = content_set['_id']
    new_id = str(ObjectId())  # Convert to string for JSON serialization
    content_set['_id'] = new_id

    # Update theme_id reference
    content_set['theme_id'] = theme_id

    # Update timestamps
    content_set['created_at'] = datetime.now(timezone.utc).isoformat()
    content_set['updated_at'] = datetime.now(timezone.utc).isoformat()

    # Initialize empty tasks array (will be populated later)
    content_set['tasks'] = []

    with open(set_path, 'w', encoding='utf-8') as f:
        json.dump(content_set, f, ensure_ascii=False, indent=2)

    print(f"Fixed set: {old_id} -> {new_id}, theme_id: {theme_id}")
    return new_id

def fix_items_file(items_path, set_id):
    """Fix items file ObjectIds and task_set_id references"""
    with open(items_path, 'r', encoding='utf-8') as f:
        items = json.load(f)
    
    item_ids = []
    
    for item in items:
        # Generate new ObjectId for item
        old_id = item['_id']
        new_id = generate_object_id()
        item['_id'] = new_id
        item_ids.append(new_id)
        
        # Update task_set_id reference
        item['task_set_id'] = set_id
        
        # Add missing fields if not present
        if 'created_at' not in item:
            item['created_at'] = datetime.now(timezone.utc).isoformat()
        if 'updated_at' not in item:
            item['updated_at'] = datetime.now(timezone.utc).isoformat()
        
        # Ensure all required fields are present
        item.setdefault('user_answer', None)
        item.setdefault('result', None)
        item.setdefault('remark', None)
        item.setdefault('scored', 0)
        item.setdefault('submitted', False)
        item.setdefault('submitted_at', None)
        item.setdefault('attempts_count', 0)
        item.setdefault('metadata', {})
        
        print(f"Fixed item: {old_id} -> {new_id}")
    
    with open(items_path, 'w', encoding='utf-8') as f:
        json.dump(items, f, ensure_ascii=False, indent=2)
    
    return item_ids

def update_set_with_tasks(set_path, task_ids):
    """Update set file with task IDs"""
    with open(set_path, 'r', encoding='utf-8') as f:
        content_set = json.load(f)
    
    content_set['tasks'] = task_ids
    content_set['total_items'] = len(task_ids)
    
    with open(set_path, 'w', encoding='utf-8') as f:
        json.dump(content_set, f, ensure_ascii=False, indent=2)
    
    print(f"Updated set {content_set['_id']} with {len(task_ids)} tasks")

def process_theme_folder(theme_folder):
    """Process a complete theme folder"""
    print(f"\n=== Processing {theme_folder} ===")
    
    theme_path = os.path.join(theme_folder, 'theme.json')
    
    # Fix theme
    theme_id = fix_theme_file(theme_path)
    
    # Process all sets in the folder
    set_files = [f for f in os.listdir(theme_folder) if f.startswith('set') and f.endswith('.json') and 'items' not in f]
    
    for set_file in sorted(set_files):
        set_path = os.path.join(theme_folder, set_file)
        items_file = set_file.replace('.json', '_items.json')
        items_path = os.path.join(theme_folder, items_file)
        
        if os.path.exists(items_path):
            # Fix set
            set_id = fix_set_file(set_path, theme_id)
            
            # Fix items
            item_ids = fix_items_file(items_path, set_id)
            
            # Update set with task IDs
            update_set_with_tasks(set_path, item_ids)
        else:
            print(f"Warning: Items file {items_path} not found for {set_file}")

def main():
    """Main function to process all theme folders"""
    content_dir = 'content'
    
    if not os.path.exists(content_dir):
        print(f"Error: {content_dir} directory not found")
        return
    
    # Get all theme folders
    theme_folders = [d for d in os.listdir(content_dir) 
                    if os.path.isdir(os.path.join(content_dir, d))]
    
    print(f"Found {len(theme_folders)} theme folders: {theme_folders}")
    
    # Process each theme folder
    for theme_folder in sorted(theme_folders):
        theme_path = os.path.join(content_dir, theme_folder)
        process_theme_folder(theme_path)
    
    print("\n=== All themes processed successfully! ===")
    
    # Generate summary
    print("\n=== SUMMARY ===")
    for theme_folder in sorted(theme_folders):
        theme_path = os.path.join(content_dir, theme_folder, 'theme.json')
        with open(theme_path, 'r', encoding='utf-8') as f:
            theme = json.load(f)
        print(f"Theme: {theme['name']} ({theme['name_en']}) - ID: {theme['_id']}")

if __name__ == "__main__":
    main()
